[ERROR] 2025/07/04 22:00:05 handlers.go:150: TraceID: 6a3a8efd-2975-46b7-8382-41c67c547c55 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 22:00:05 handlers.go:154: TraceID: 6a3a8efd-2975-46b7-8382-41c67c547c55 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 22:00:05 handlers.go:150: TraceID: 9156bc70-b9f4-4c4e-a56e-1c5f9c19a29a | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 22:00:05 handlers.go:154: TraceID: 9156bc70-b9f4-4c4e-a56e-1c5f9c19a29a | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 22:00:05 handlers.go:159: TraceID: 6a3a8efd-2975-46b7-8382-41c67c547c55 | Stack Trace:
goroutine 373 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400028a010, 0x14000246000, {0x1400029e520?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400028a010, {0x105736cc8?, 0x14000321c80?}, {0x1400029e520, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140002ba1c0?, {0x105736cc8?, 0x14000321c80?}, {0x1400029e520?, 0x14000010210?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140002ba1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:132 +0x198
desktop/backend.(*App).GetConfigInfo(0x140002ba1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1056e8cc0?, 0x140002ba1b0?, 0x140004e6c78?}, {0x1050b8422, 0x4}, {0x106113480, 0x0, 0x104cc3844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1056e8cc0?, 0x140002ba1b0?, 0x0?}, {0x106113480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140002eb040, {0x1057476b0, 0x140000bb650}, {0x106113480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 372
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 22:00:05 handlers.go:164: TraceID: 6a3a8efd-2975-46b7-8382-41c67c547c55 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 22:00:05 handlers.go:159: TraceID: 9156bc70-b9f4-4c4e-a56e-1c5f9c19a29a | Stack Trace:
goroutine 376 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400028a010, 0x1400023a000, {0x14000490180?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400028a010, {0x105736cc8?, 0x14000711320?}, {0x14000490180, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140002ba1c0?, {0x105736cc8?, 0x14000711320?}, {0x14000490180?, 0x14000010210?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140002ba1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:132 +0x198
desktop/backend.(*App).GetRemotes(0x140002ba1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:185 +0x78
reflect.Value.call({0x1056e8cc0?, 0x140002ba1b0?, 0x14000018c78?}, {0x1050b8422, 0x4}, {0x106113480, 0x0, 0x104cc3844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1056e8cc0?, 0x140002ba1b0?, 0x14000018da8?}, {0x106113480?, 0x14000018d38?, 0x10454b254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140002eb220, {0x1057476b0, 0x1400026ac60}, {0x106113480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 375
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 22:00:05 handlers.go:164: TraceID: 9156bc70-b9f4-4c4e-a56e-1c5f9c19a29a | Underlying Error: open .config/.profiles: no such file or directory
